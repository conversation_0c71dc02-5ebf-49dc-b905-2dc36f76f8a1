"""
Tests for the elm_commands/environment.py module.
"""
import subprocess
import sys
import pytest
import pandas as pd
from unittest.mock import patch, MagicMock
import click

from elm.elm_commands.environment import (
    ensure_db_driver_installed,
    AliasedGroup,
    DB_PACKAGES
)


class TestEnsureDbDriverInstalled:
    """Test ensure_db_driver_installed function."""

    def test_ensure_db_driver_installed_unknown_db_type(self):
        """Test with unknown database type."""
        # Should return early without doing anything
        result = ensure_db_driver_installed("UNKNOWN_DB")
        assert result is None

    def test_ensure_db_driver_installed_already_installed_psycopg2(self):
        """Test when psycopg2-binary is already installed."""
        with patch('builtins.__import__') as mock_import:
            mock_import.return_value = MagicMock()

            result = ensure_db_driver_installed("POSTGRES")

            # Should import psycopg2 for psycopg2-binary package
            mock_import.assert_called_once_with("psycopg2")
            assert result is None

    def test_ensure_db_driver_installed_already_installed_other(self):
        """Test when other database driver is already installed."""
        with patch('builtins.__import__') as mock_import:
            mock_import.return_value = MagicMock()

            result = ensure_db_driver_installed("MYSQL")

            # Should import pymysql
            mock_import.assert_called_once_with("pymysql")
            assert result is None

    def test_ensure_db_driver_installed_not_installed_success(self):
        """Test installing driver when not installed - success case."""
        with patch('builtins.__import__') as mock_import, \
             patch('subprocess.check_call') as mock_subprocess, \
             patch('builtins.print') as mock_print:

            # First call raises ImportError (not installed)
            mock_import.side_effect = ImportError("No module named 'pymysql'")
            mock_subprocess.return_value = None  # Successful installation

            result = ensure_db_driver_installed("MYSQL")

            # Should try to install the package
            mock_subprocess.assert_called_once_with([
                sys.executable, "-m", "pip", "install", "pymysql"
            ])
            mock_print.assert_any_call("Installing required database driver: pymysql")
            mock_print.assert_any_call("Successfully installed pymysql")
            assert result is None

    def test_ensure_db_driver_installed_not_installed_failure(self):
        """Test installing driver when not installed - failure case."""
        with patch('builtins.__import__') as mock_import, \
             patch('subprocess.check_call') as mock_subprocess, \
             patch('builtins.print') as mock_print:

            # First call raises ImportError (not installed)
            mock_import.side_effect = ImportError("No module named 'cx_oracle'")
            # Installation fails
            mock_subprocess.side_effect = subprocess.CalledProcessError(1, "pip install")

            result = ensure_db_driver_installed("ORACLE")

            # Should try to install and handle failure
            mock_subprocess.assert_called_once_with([
                sys.executable, "-m", "pip", "install", "cx_oracle"
            ])
            mock_print.assert_any_call("Installing required database driver: cx_oracle")
            mock_print.assert_any_call("Failed to install cx_oracle: Command 'pip install' returned non-zero exit status 1.")
            mock_print.assert_any_call("Please install cx_oracle manually using: pip install cx_oracle")
            assert result is None

    def test_ensure_db_driver_installed_package_name_with_version(self):
        """Test with package name that has version specifier."""
        # Modify DB_PACKAGES temporarily to test version handling
        original_packages = DB_PACKAGES.copy()
        DB_PACKAGES["TEST_DB"] = "test-package>=1.0.0"

        try:
            with patch('builtins.__import__') as mock_import:
                mock_import.return_value = MagicMock()

                result = ensure_db_driver_installed("TEST_DB")

                # Should import test_package (before the >)
                mock_import.assert_called_once_with("test_package")
                assert result is None
        finally:
            # Restore original packages
            DB_PACKAGES.clear()
            DB_PACKAGES.update(original_packages)

    def test_ensure_db_driver_installed_package_name_with_dash(self):
        """Test with package name that has dashes."""
        # Modify DB_PACKAGES temporarily to test dash handling
        original_packages = DB_PACKAGES.copy()
        DB_PACKAGES["TEST_DB"] = "test-package-name"

        try:
            with patch('builtins.__import__') as mock_import:
                mock_import.return_value = MagicMock()

                result = ensure_db_driver_installed("TEST_DB")

                # Should import test_package_name (dashes replaced with underscores)
                mock_import.assert_called_once_with("test_package_name")
                assert result is None
        finally:
            # Restore original packages
            DB_PACKAGES.clear()
            DB_PACKAGES.update(original_packages)

    def test_db_packages_constants(self):
        """Test that DB_PACKAGES contains expected database types."""
        expected_packages = {
            "ORACLE": "cx_oracle",
            "MYSQL": "pymysql",
            "MSSQL": "pyodbc",
            "POSTGRES": "psycopg2-binary"
        }

        assert DB_PACKAGES == expected_packages


class TestEnvironmentAliasedGroup:
    """Test AliasedGroup functionality for environment commands."""

    def test_aliased_group_get_command_with_alias(self):
        """Test getting command with alias."""
        # Create mock commands
        mock_create = MagicMock()
        mock_create.name = 'create'

        # Create aliases dict
        aliases = {'new': mock_create}

        group = AliasedGroup()

        with patch('elm.elm_commands.environment.ALIASES', aliases):
            with patch.object(click.Group, 'get_command') as mock_super:
                mock_super.return_value = mock_create

                result = group.get_command(None, 'new')

                # Should call super with the resolved command name
                mock_super.assert_called_with(None, 'create')
                assert result == mock_create

    def test_aliased_group_get_command_without_alias(self):
        """Test getting command without alias."""
        group = AliasedGroup()

        with patch('elm.elm_commands.environment.ALIASES', {}):
            with patch.object(click.Group, 'get_command') as mock_super:
                mock_super.return_value = None

                result = group.get_command(None, 'unknown')

                # Should call super with the original command name
                mock_super.assert_called_with(None, 'unknown')
                assert result is None

    def test_aliased_group_get_command_none_cmd_name(self):
        """Test getting command with None cmd_name."""
        group = AliasedGroup()

        with patch('elm.elm_commands.environment.ALIASES', {}):
            result = group.get_command(None, None)

            # Should return None for None cmd_name
            assert result is None


class TestEnvironmentCLICommands:
    """Test CLI command functions for environment commands."""

    @patch('elm.elm_commands.environment.core_env.create_environment')
    def test_create_environment_success_case(self, mock_create):
        """Test create environment success case."""
        mock_create.return_value = MagicMock(success=True, message="Environment created")

        from elm.elm_commands.environment import create

        with patch('click.echo') as mock_echo:
            create(
                name='test-env',
                host='localhost',
                port=5432,
                user='postgres',
                password='password',
                service='mydb',
                type='POSTGRES',
                encrypt=False,
                encryption_key=None,
                overwrite=False,
                user_input=False
            )

            mock_echo.assert_called_with("Environment created successfully")
            mock_create.assert_called_once()

    @patch('elm.elm_commands.environment.core_env.create_environment')
    def test_create_environment_failure_case(self, mock_create):
        """Test create environment failure case."""
        mock_create.return_value = MagicMock(success=False, message="Creation failed")

        from elm.elm_commands.environment import create

        with pytest.raises(click.UsageError) as exc_info:
            create(
                name='test-env',
                host='localhost',
                port=5432,
                user='postgres',
                password='password',
                service='mydb',
                type='POSTGRES',
                encrypt=False,
                encryption_key=None,
                overwrite=False,
                user_input=False
            )

            assert "Creation failed" in str(exc_info.value)

    @patch('elm.elm_commands.environment.core_env.delete_environment')
    def test_delete_environment_success(self, mock_delete):
        """Test delete environment successfully."""
        mock_delete.return_value = MagicMock(success=True, message="Environment deleted")

        from elm.elm_commands.environment import delete

        with patch('click.echo') as mock_echo:
            delete(name='test-env')

            mock_echo.assert_called_with("Environment 'test-env' deleted successfully")
            mock_delete.assert_called_once_with(name='test-env')

    @patch('elm.elm_commands.environment.core_env.delete_environment')
    def test_delete_environment_failure(self, mock_delete):
        """Test delete environment failure."""
        mock_delete.return_value = MagicMock(success=False, message="Environment not found")

        from elm.elm_commands.environment import delete

        with pytest.raises(click.UsageError) as exc_info:
            delete(name='nonexistent-env')

            assert "Environment not found" in str(exc_info.value)

    @patch('elm.elm_commands.environment.core_env.get_environment')
    def test_show_environment_success(self, mock_get):
        """Test show environment successfully."""
        mock_get.return_value = MagicMock(
            success=True,
            data={'name': 'test-env', 'host': 'localhost', 'port': '5432', 'user': 'postgres'}
        )

        from elm.elm_commands.environment import show

        with patch('click.echo') as mock_echo:
            show(name='test-env', encryption_key=None)

            mock_get.assert_called_once_with(name='test-env', encryption_key=None)
            # Should echo environment details
            assert mock_echo.call_count >= 3  # At least host, port, user

    @patch('elm.elm_commands.environment.core_env.get_environment')
    def test_show_environment_failure(self, mock_get):
        """Test show environment failure."""
        mock_get.return_value = MagicMock(success=False, message="Environment not found")

        from elm.elm_commands.environment import show

        with pytest.raises(click.UsageError) as exc_info:
            show(name='nonexistent-env', encryption_key=None)

            assert "Environment not found" in str(exc_info.value)

    @patch('elm.elm_commands.environment.core_env.update_environment')
    def test_update_environment_success(self, mock_update):
        """Test update environment successfully."""
        mock_update.return_value = MagicMock(success=True, message="Environment updated")

        from elm.elm_commands.environment import update

        with patch('click.echo') as mock_echo:
            update(
                name='test-env',
                host='new-host',
                port=None,
                user=None,
                password=None,
                service=None,
                type=None,
                encrypt=None,
                encryption_key=None
            )

            mock_echo.assert_called_with("Environment 'test-env' updated successfully")
            mock_update.assert_called_once()

    @patch('elm.elm_commands.environment.core_env.update_environment')
    def test_update_environment_failure(self, mock_update):
        """Test update environment failure."""
        mock_update.return_value = MagicMock(success=False, message="Update failed")

        from elm.elm_commands.environment import update

        with pytest.raises(click.UsageError) as exc_info:
            update(
                name='test-env',
                host='new-host',
                port=None,
                user=None,
                password=None,
                service=None,
                type=None,
                encrypt=None,
                encryption_key=None
            )

            assert "Update failed" in str(exc_info.value)

    @patch('elm.elm_commands.environment.core_env.test_environment')
    def test_test_environment_success(self, mock_test):
        """Test test environment successfully."""
        mock_test.return_value = MagicMock(success=True, message="Connection successful")

        from elm.elm_commands.environment import test

        with patch('click.echo') as mock_echo:
            result = test(name='test-env', encryption_key=None)

            mock_echo.assert_called_with("✓ Connection successful")
            mock_test.assert_called_once_with(name='test-env', encryption_key=None)
            assert result is True

    @patch('elm.elm_commands.environment.core_env.test_environment')
    def test_test_environment_failure(self, mock_test):
        """Test test environment failure."""
        mock_test.return_value = MagicMock(success=False, message="Connection failed")

        from elm.elm_commands.environment import test

        with patch('click.echo') as mock_echo:
            result = test(name='test-env', encryption_key=None)

            mock_echo.assert_called_with("✗ Connection failed")
            mock_test.assert_called_once_with(name='test-env', encryption_key=None)
            assert result is False

    @patch('elm.elm_commands.environment.core_env.execute_sql')
    def test_execute_sql_success(self, mock_execute):
        """Test execute SQL successfully."""
        mock_execute.return_value = MagicMock(
            success=True,
            data=pd.DataFrame({'id': [1, 2], 'name': ['A', 'B']}),
            record_count=2
        )

        from elm.elm_commands.environment import execute

        with patch('click.echo') as mock_echo:
            execute(
                name='test-env',
                query='SELECT * FROM test',
                encryption_key=None
            )

            mock_echo.assert_called()
            mock_execute.assert_called_once()

    @patch('elm.elm_commands.environment.core_env.execute_sql')
    def test_execute_sql_no_results(self, mock_execute):
        """Test execute SQL with no results."""
        mock_execute.return_value = MagicMock(
            success=True,
            data=None,
            message="Query executed successfully. No rows returned."
        )

        from elm.elm_commands.environment import execute

        with patch('click.echo') as mock_echo:
            execute(
                name='test-env',
                query='DELETE FROM test',
                encryption_key=None
            )

            mock_echo.assert_called_with("Query executed successfully. No rows returned.")

    @patch('elm.elm_commands.environment.core_env.execute_sql')
    def test_execute_sql_failure(self, mock_execute):
        """Test execute SQL with failure."""
        mock_execute.return_value = MagicMock(success=False, message="SQL execution failed")

        from elm.elm_commands.environment import execute

        with patch('click.echo') as mock_echo:
            execute(
                name='test-env',
                query='INVALID SQL',
                encryption_key=None
            )

            mock_echo.assert_called_with("SQL execution failed")


class TestEnvironmentCLIEdgeCases:
    """Test edge cases for environment CLI commands."""

    def test_create_environment_missing_required_fields(self):
        """Test create environment with missing required fields."""
        from elm.elm_commands.environment import create

        with pytest.raises(click.UsageError) as exc_info:
            create(
                name='test-env',
                host=None,  # Missing required field
                port=5432,
                user='postgres',
                password='password',
                service='mydb',
                type='POSTGRES',
                encrypt=False,
                encryption_key=None,
                overwrite=False,
                user_input=False
            )

        assert "Missing required fields" in str(exc_info.value)

    def test_create_environment_encrypt_without_key(self):
        """Test create environment with encrypt=True but no encryption key."""
        from elm.elm_commands.environment import create

        with pytest.raises(click.UsageError) as exc_info:
            create(
                name='test-env',
                host='localhost',
                port=5432,
                user='postgres',
                password='password',
                service='mydb',
                type='POSTGRES',
                encrypt=True,
                encryption_key=None,  # Missing encryption key
                overwrite=False,
                user_input=False
            )

        assert "encryption-key" in str(exc_info.value).lower()

    @patch('elm.elm_commands.environment.core_env.create_environment')
    def test_create_environment_already_exists_no_overwrite(self, mock_create):
        """Test create environment when it already exists without overwrite."""
        mock_create.return_value = MagicMock(success=False, message="Environment already exists")

        from elm.elm_commands.environment import create

        with pytest.raises(click.UsageError) as exc_info:
            create(
                name='existing-env',
                host='localhost',
                port=5432,
                user='postgres',
                password='password',
                service='mydb',
                type='POSTGRES',
                encrypt=False,
                encryption_key=None,
                overwrite=False,
                user_input=False
            )

        assert "Environment already exists" in str(exc_info.value)

    @patch('click.prompt')
    @patch('click.confirm')
    def test_create_environment_user_input_mode(self, mock_confirm, mock_prompt):
        """Test create environment with user input mode."""
        # Mock user inputs
        mock_prompt.side_effect = [
            'localhost',  # host
            5432,         # port
            'postgres',   # user
            'password',   # password
            'password',   # password confirmation
            'mydb',       # service
            'POSTGRES',   # type
        ]
        mock_confirm.return_value = False  # Don't encrypt

        from elm.elm_commands.environment import create

        with patch('elm.elm_commands.environment.core_env.create_environment') as mock_create:
            mock_create.return_value = MagicMock(success=True, message="Environment created")

            with patch('click.echo') as mock_echo:
                create(
                    name='test-env',
                    host=None,
                    port=None,
                    user=None,
                    password=None,
                    service=None,
                    type=None,
                    encrypt=None,
                    encryption_key=None,
                    overwrite=False,
                    user_input=True
                )

                # Should prompt for all required fields
                assert mock_prompt.call_count == 7
                mock_confirm.assert_called_once()
                mock_echo.assert_called_with("Environment created successfully")

    @patch('click.prompt')
    @patch('click.confirm')
    def test_create_environment_user_input_with_encryption(self, mock_confirm, mock_prompt):
        """Test create environment with user input mode and encryption."""
        # Mock user inputs
        mock_prompt.side_effect = [
            'localhost',  # host
            5432,         # port
            'postgres',   # user
            'password',   # password
            'password',   # password confirmation
            'mydb',       # service
            'POSTGRES',   # type
            'secret',     # encryption key
            'secret',     # encryption key confirmation
        ]
        mock_confirm.return_value = True  # Encrypt

        from elm.elm_commands.environment import create

        with patch('elm.elm_commands.environment.core_env.create_environment') as mock_create:
            mock_create.return_value = MagicMock(success=True, message="Environment created")

            with patch('click.echo') as mock_echo:
                create(
                    name='test-env',
                    host=None,
                    port=None,
                    user=None,
                    password=None,
                    service=None,
                    type=None,
                    encrypt=None,
                    encryption_key=None,
                    overwrite=False,
                    user_input=True
                )

                # Should prompt for all required fields + encryption
                assert mock_prompt.call_count == 9
                mock_confirm.assert_called_once()
                mock_echo.assert_called_with("Environment created successfully")

    @patch('click.prompt')
    def test_create_environment_password_mismatch(self, mock_prompt):
        """Test create environment with password mismatch."""
        # Mock user inputs with mismatched passwords
        mock_prompt.side_effect = [
            'localhost',  # host
            5432,         # port
            'postgres',   # user
            'password1',  # password
            'password2',  # password confirmation (different)
            'password',   # password retry
            'password',   # password confirmation (matching)
            'mydb',       # service
            'POSTGRES',   # type
        ]

        from elm.elm_commands.environment import create

        with patch('click.confirm') as mock_confirm:
            mock_confirm.return_value = False  # Don't encrypt

            with patch('elm.elm_commands.environment.core_env.create_environment') as mock_create:
                mock_create.return_value = MagicMock(success=True, message="Environment created")

                with patch('click.echo') as mock_echo:
                    create(
                        name='test-env',
                        host=None,
                        port=None,
                        user=None,
                        password=None,
                        service=None,
                        type=None,
                        encrypt=None,
                        encryption_key=None,
                        overwrite=False,
                        user_input=True
                    )

                    # Should prompt for passwords multiple times due to mismatch
                    assert mock_prompt.call_count == 9  # 7 + 2 extra for password retry